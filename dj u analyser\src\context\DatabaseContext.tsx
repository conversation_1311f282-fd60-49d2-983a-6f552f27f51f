import React, { createContext, useContext, useEffect, useState } from 'react';
import { Database, Song, Playlist } from '../database/Database';
import { AnalysisResult } from '../services/AudioAnalyzer';
import { MediaInfo } from '../services/MediaExtractor';

interface DatabaseContextType {
  database: Database;
  songs: Song[];
  playlists: Playlist[];
  loading: boolean;
  error: string | null;
  
  // Song operations
  addSong: (mediaInfo: MediaInfo, analysisResult?: AnalysisResult) => Promise<number>;
  updateSongAnalysis: (songId: number, analysisResult: AnalysisResult) => Promise<void>;
  searchSongs: (query: string) => Promise<Song[]>;
  updateSongRating: (songId: number, rating: number) => Promise<void>;
  deleteSong: (id: number) => Promise<void>;
  refreshSongs: () => Promise<void>;
  
  // Playlist operations
  createPlaylist: (name: string, description?: string) => Promise<number>;
  addSongToPlaylist: (playlistId: number, songId: number) => Promise<void>;
  getPlaylistSongs: (playlistId: number) => Promise<Song[]>;
  removeSongFromPlaylist: (playlistId: number, songId: number) => Promise<void>;
  deletePlaylist: (id: number) => Promise<void>;
  refreshPlaylists: () => Promise<void>;
  
  // Analytics
  getAnalysisStats: () => Promise<{totalSongs: number, avgBpm: number, genreDistribution: {[key: string]: number}}>;
}

const DatabaseContext = createContext<DatabaseContextType | undefined>(undefined);

export const DatabaseProvider: React.FC<{children: React.ReactNode}> = ({ children }) => {
  const [database] = useState(() => new Database());
  const [songs, setSongs] = useState<Song[]>([]);
  const [playlists, setPlaylists] = useState<Playlist[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    initializeData();
  }, []);

  const initializeData = async () => {
    try {
      setLoading(true);
      await refreshSongs();
      await refreshPlaylists();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to initialize database');
    } finally {
      setLoading(false);
    }
  };

  const refreshSongs = async () => {
    try {
      const songList = await database.getSongs();
      setSongs(songList);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load songs');
    }
  };

  const refreshPlaylists = async () => {
    try {
      const playlistList = await database.getPlaylists();
      setPlaylists(playlistList);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load playlists');
    }
  };

  const addSong = async (mediaInfo: MediaInfo, analysisResult?: AnalysisResult): Promise<number> => {
    try {
      const songId = await database.addSong(mediaInfo, analysisResult);
      await refreshSongs();
      return songId;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add song';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const updateSongAnalysis = async (songId: number, analysisResult: AnalysisResult): Promise<void> => {
    try {
      await database.updateSongAnalysis(songId, analysisResult);
      await refreshSongs();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update song analysis';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const searchSongs = async (query: string): Promise<Song[]> => {
    try {
      return await database.searchSongs(query);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to search songs';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const updateSongRating = async (songId: number, rating: number): Promise<void> => {
    try {
      await database.updateSongRating(songId, rating);
      await refreshSongs();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update song rating';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const deleteSong = async (id: number): Promise<void> => {
    try {
      await database.deleteSong(id);
      await refreshSongs();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete song';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const createPlaylist = async (name: string, description: string = ''): Promise<number> => {
    try {
      const playlistId = await database.createPlaylist(name, description);
      await refreshPlaylists();
      return playlistId;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create playlist';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const addSongToPlaylist = async (playlistId: number, songId: number): Promise<void> => {
    try {
      await database.addSongToPlaylist(playlistId, songId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to add song to playlist';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const getPlaylistSongs = async (playlistId: number): Promise<Song[]> => {
    try {
      return await database.getPlaylistSongs(playlistId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get playlist songs';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const removeSongFromPlaylist = async (playlistId: number, songId: number): Promise<void> => {
    try {
      await database.removeSongFromPlaylist(playlistId, songId);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to remove song from playlist';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const deletePlaylist = async (id: number): Promise<void> => {
    try {
      await database.deletePlaylist(id);
      await refreshPlaylists();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete playlist';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const getAnalysisStats = async () => {
    try {
      return await database.getAnalysisStats();
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to get analysis stats';
      setError(errorMessage);
      throw new Error(errorMessage);
    }
  };

  const value: DatabaseContextType = {
    database,
    songs,
    playlists,
    loading,
    error,
    addSong,
    updateSongAnalysis,
    searchSongs,
    updateSongRating,
    deleteSong,
    refreshSongs,
    createPlaylist,
    addSongToPlaylist,
    getPlaylistSongs,
    removeSongFromPlaylist,
    deletePlaylist,
    refreshPlaylists,
    getAnalysisStats,
  };

  return (
    <DatabaseContext.Provider value={value}>
      {children}
    </DatabaseContext.Provider>
  );
};

export const useDatabase = (): DatabaseContextType => {
  const context = useContext(DatabaseContext);
  if (context === undefined) {
    throw new Error('useDatabase must be used within a DatabaseProvider');
  }
  return context;
};
