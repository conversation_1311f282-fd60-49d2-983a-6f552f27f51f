import { AudioAnalyzer } from '../AudioAnalyzer';

describe('AudioAnalyzer', () => {
  let analyzer: AudioAnalyzer;

  beforeEach(() => {
    analyzer = new AudioAnalyzer();
  });

  describe('analyzeAudio', () => {
    it('should analyze audio buffer and return results', async () => {
      // Create a mock audio buffer (sine wave at 440Hz)
      const sampleRate = 44100;
      const duration = 2; // 2 seconds
      const frequency = 440; // A4 note
      const bufferLength = sampleRate * duration;
      const audioBuffer = new Float32Array(bufferLength);

      // Generate sine wave
      for (let i = 0; i < bufferLength; i++) {
        audioBuffer[i] = Math.sin(2 * Math.PI * frequency * i / sampleRate) * 0.5;
      }

      const result = await analyzer.analyzeAudio(audioBuffer);

      expect(result).toBeDefined();
      expect(result.bpm).toBeGreaterThan(0);
      expect(result.key).toBeDefined();
      expect(result.genre).toBeDefined();
      expect(result.confidence).toBeDefined();
      expect(result.features).toBeDefined();
    });

    it('should handle empty audio buffer', async () => {
      const audioBuffer = new Float32Array(0);

      await expect(analyzer.analyzeAudio(audioBuffer)).rejects.toThrow();
    });

    it('should return confidence scores between 0 and 1', async () => {
      const audioBuffer = new Float32Array(44100); // 1 second of silence
      
      const result = await analyzer.analyzeAudio(audioBuffer);

      expect(result.confidence.bpm).toBeGreaterThanOrEqual(0);
      expect(result.confidence.bpm).toBeLessThanOrEqual(1);
      expect(result.confidence.key).toBeGreaterThanOrEqual(0);
      expect(result.confidence.key).toBeLessThanOrEqual(1);
      expect(result.confidence.genre).toBeGreaterThanOrEqual(0);
      expect(result.confidence.genre).toBeLessThanOrEqual(1);
    });
  });

  describe('BPM detection', () => {
    it('should detect reasonable BPM values', async () => {
      // Create a rhythmic pattern
      const sampleRate = 44100;
      const bpm = 120;
      const beatInterval = (60 / bpm) * sampleRate; // samples per beat
      const audioBuffer = new Float32Array(sampleRate * 4); // 4 seconds

      // Add beats every beatInterval samples
      for (let i = 0; i < audioBuffer.length; i += Math.floor(beatInterval)) {
        if (i < audioBuffer.length) {
          // Add a short burst of noise to simulate a beat
          for (let j = 0; j < 1000 && i + j < audioBuffer.length; j++) {
            audioBuffer[i + j] = Math.random() * 0.5;
          }
        }
      }

      const result = await analyzer.analyzeAudio(audioBuffer);

      expect(result.bpm).toBeGreaterThan(60);
      expect(result.bpm).toBeLessThan(200);
    });
  });

  describe('Key detection', () => {
    it('should detect valid musical keys', async () => {
      const audioBuffer = new Float32Array(44100 * 2);
      
      // Generate a simple chord progression in C major
      const frequencies = [261.63, 329.63, 392.00]; // C, E, G
      for (let i = 0; i < audioBuffer.length; i++) {
        let sample = 0;
        frequencies.forEach(freq => {
          sample += Math.sin(2 * Math.PI * freq * i / 44100);
        });
        audioBuffer[i] = sample / frequencies.length * 0.3;
      }

      const result = await analyzer.analyzeAudio(audioBuffer);

      const validKeys = ['C', 'C#', 'D', 'D#', 'E', 'F', 'F#', 'G', 'G#', 'A', 'A#', 'B'];
      expect(validKeys).toContain(result.key);
    });
  });

  describe('Feature extraction', () => {
    it('should extract valid audio features', async () => {
      const audioBuffer = new Float32Array(44100); // 1 second
      
      // Fill with white noise
      for (let i = 0; i < audioBuffer.length; i++) {
        audioBuffer[i] = (Math.random() - 0.5) * 0.1;
      }

      const result = await analyzer.analyzeAudio(audioBuffer);

      expect(result.features.spectralCentroid).toBeGreaterThan(0);
      expect(result.features.spectralRolloff).toBeGreaterThan(0);
      expect(result.features.mfcc).toHaveLength(13);
      expect(result.features.chroma).toHaveLength(12);
      expect(result.features.tempo).toBeGreaterThan(0);
    });

    it('should return normalized chroma features', async () => {
      const audioBuffer = new Float32Array(44100);
      
      const result = await analyzer.analyzeAudio(audioBuffer);

      // Chroma features should sum to approximately 1 (normalized)
      const chromaSum = result.features.chroma.reduce((sum, val) => sum + val, 0);
      expect(chromaSum).toBeCloseTo(1, 1);
    });
  });

  describe('Error handling', () => {
    it('should handle invalid input gracefully', async () => {
      const invalidBuffer = null as any;

      await expect(analyzer.analyzeAudio(invalidBuffer)).rejects.toThrow();
    });

    it('should handle very short audio buffers', async () => {
      const shortBuffer = new Float32Array(100); // Very short buffer

      await expect(analyzer.analyzeAudio(shortBuffer)).rejects.toThrow();
    });
  });
});
