import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { Card, Text } from 'react-native-paper';
import { PieChart } from 'react-native-chart-kit';

interface StatsCardProps {
  stats: {
    totalSongs: number;
    avgBpm: number;
    genreDistribution: { [key: string]: number };
  };
}

const StatsCard: React.FC<StatsCardProps> = ({ stats }) => {
  const screenWidth = Dimensions.get('window').width;
  const chartWidth = screenWidth - 64; // Account for margins

  const generateChartData = () => {
    const colors = [
      '#FF6384',
      '#36A2EB',
      '#FFCE56',
      '#4BC0C0',
      '#9966FF',
      '#FF9F40',
      '#FF6384',
      '#C9CBCF',
    ];

    return Object.entries(stats.genreDistribution).map(([genre, count], index) => ({
      name: genre,
      population: count,
      color: colors[index % colors.length],
      legendFontColor: '#ffffff',
      legendFontSize: 12,
    }));
  };

  const chartConfig = {
    backgroundGradientFrom: '#16213e',
    backgroundGradientTo: '#16213e',
    color: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
    strokeWidth: 2,
    barPercentage: 0.5,
    useShadowColorFromDataset: false,
  };

  return (
    <Card style={styles.card}>
      <Card.Title title="Library Statistics" titleStyle={styles.cardTitle} />
      <Card.Content>
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>{stats.totalSongs}</Text>
            <Text style={styles.statLabel}>Total Songs</Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>
              {stats.avgBpm ? Math.round(stats.avgBpm) : 0}
            </Text>
            <Text style={styles.statLabel}>Avg BPM</Text>
          </View>
          
          <View style={styles.statItem}>
            <Text style={styles.statNumber}>
              {Object.keys(stats.genreDistribution).length}
            </Text>
            <Text style={styles.statLabel}>Genres</Text>
          </View>
        </View>

        {Object.keys(stats.genreDistribution).length > 0 && (
          <View style={styles.chartContainer}>
            <Text style={styles.chartTitle}>Genre Distribution</Text>
            <PieChart
              data={generateChartData()}
              width={chartWidth}
              height={200}
              chartConfig={chartConfig}
              accessor="population"
              backgroundColor="transparent"
              paddingLeft="15"
              center={[10, 0]}
              absolute
            />
          </View>
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#16213e',
    margin: 16,
  },
  cardTitle: {
    color: '#ffffff',
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    color: '#ffffff',
    fontSize: 24,
    fontWeight: 'bold',
  },
  statLabel: {
    color: '#cccccc',
    fontSize: 12,
    marginTop: 4,
  },
  chartContainer: {
    alignItems: 'center',
  },
  chartTitle: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 10,
  },
});

export default StatsCard;
