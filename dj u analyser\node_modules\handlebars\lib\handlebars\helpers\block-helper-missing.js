import { appendContextPath, create<PERSON>rame, isArray } from '../utils';

export default function(instance) {
  instance.registerHelper('blockHelperMissing', function(context, options) {
    let inverse = options.inverse,
      fn = options.fn;

    if (context === true) {
      return fn(this);
    } else if (context === false || context == null) {
      return inverse(this);
    } else if (isArray(context)) {
      if (context.length > 0) {
        if (options.ids) {
          options.ids = [options.name];
        }

        return instance.helpers.each(context, options);
      } else {
        return inverse(this);
      }
    } else {
      if (options.data && options.ids) {
        let data = createFrame(options.data);
        data.contextPath = appendContextPath(
          options.data.contextPath,
          options.name
        );
        options = { data: data };
      }

      return fn(context, options);
    }
  });
}
