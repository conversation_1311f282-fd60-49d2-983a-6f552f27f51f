import React from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';

interface RatingStarsProps {
  rating: number;
  onRatingChange?: (rating: number) => void;
  size?: number;
  readonly?: boolean;
}

const RatingStars: React.FC<RatingStarsProps> = ({
  rating,
  onRatingChange,
  size = 20,
  readonly = false,
}) => {
  const handleStarPress = (starRating: number) => {
    if (!readonly && onRatingChange) {
      onRatingChange(starRating);
    }
  };

  const renderStar = (starNumber: number) => {
    const isFilled = starNumber <= rating;
    const StarComponent = readonly ? View : TouchableOpacity;

    return (
      <StarComponent
        key={starNumber}
        onPress={() => handleStarPress(starNumber)}
        style={styles.starContainer}
      >
        <Icon
          name={isFilled ? 'star' : 'star-border'}
          size={size}
          color={isFilled ? '#ffd700' : '#666666'}
        />
      </StarComponent>
    );
  };

  return (
    <View style={styles.container}>
      {[1, 2, 3, 4, 5].map(renderStar)}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  starContainer: {
    marginHorizontal: 1,
  },
});

export default RatingStars;
