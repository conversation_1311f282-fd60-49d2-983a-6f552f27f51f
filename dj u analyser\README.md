# DJ Analyzer

An autonomous AI agent for music track analysis with BPM, key, and genre detection. Built with React Native and Expo for cross-platform mobile deployment.

## Features

- 🎵 **Audio Analysis**: Advanced BPM detection, key detection, and genre classification
- 🌐 **Multi-Source Support**: Analyze music from YouTube, SoundCloud, or local files
- 📱 **Mobile-First**: Native mobile app with intuitive touch interface
- 📊 **Visualizations**: Interactive charts showing audio features and analysis results
- 🎼 **Playlist Management**: Create, organize, and share playlists
- ⭐ **Rating System**: Rate and organize your music collection
- 🔍 **Smart Search**: Find songs by title, artist, genre, or audio features
- 📤 **Export/Import**: Share playlists in multiple formats (JSON, M3U, text)

## Technology Stack

- **Frontend**: React Native with Expo
- **UI Framework**: React Native Paper (Material Design)
- **Database**: SQLite with Expo SQLite
- **Audio Processing**: Custom audio analysis engine with FFT
- **Charts**: React Native Chart Kit
- **Navigation**: React Navigation 6
- **State Management**: React Context API

## Installation

### Prerequisites

- Node.js (v16 or higher)
- npm or yarn
- Expo CLI (`npm install -g @expo/cli`)
- Android Studio (for Android development)
- Xcode (for iOS development, macOS only)

### Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd dj-analyzer
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Configure API Keys** (Optional)
   
   For YouTube and SoundCloud support, update the API keys in `src/services/MediaExtractor.ts`:
   ```typescript
   private readonly YOUTUBE_API_KEY = 'your_youtube_api_key';
   private readonly SOUNDCLOUD_CLIENT_ID = 'your_soundcloud_client_id';
   ```

4. **Start the development server**
   ```bash
   npm start
   ```

## Building for Production

### Android APK

1. **Install EAS CLI**
   ```bash
   npm install -g eas-cli
   ```

2. **Login to Expo**
   ```bash
   eas login
   ```

3. **Build APK**
   ```bash
   eas build --platform android --profile production
   ```

### iOS App

```bash
eas build --platform ios --profile production
```

## Usage

### Analyzing Music

1. **From URL**: Paste a YouTube or SoundCloud URL in the Analyze tab
2. **From File**: Select an audio file from your device
3. **View Results**: See BPM, key, genre, and detailed audio features
4. **Save to Library**: Add analyzed songs to your collection

### Managing Playlists

1. **Create Playlist**: Tap the + button in the Playlists tab
2. **Add Songs**: Use the menu on any song to add it to playlists
3. **Share Playlist**: Export playlists in JSON, M3U, or text format
4. **Import Playlist**: Import playlists from other apps or users

### Search and Filter

- Use the search bar to find songs by title, artist, or genre
- Filter by genre using the genre chips
- Sort by BPM, rating, or date added

## Audio Analysis Features

### BPM Detection
- Onset detection using spectral flux
- Peak finding in tempo histogram
- Confidence scoring for accuracy assessment

### Key Detection
- Chroma feature extraction
- Correlation with key profiles
- Support for major and minor keys

### Genre Classification
- Spectral feature analysis (centroid, rolloff)
- MFCC (Mel-Frequency Cepstral Coefficients)
- Tempo-based heuristic classification

### Audio Features
- **Spectral Centroid**: Brightness of the sound
- **Spectral Rolloff**: Frequency below which 85% of energy is contained
- **MFCC**: Coefficients representing the shape of the spectral envelope
- **Chroma**: Pitch class distribution
- **Tempo**: Perceived speed of the music

## API Configuration

### YouTube Data API

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing
3. Enable YouTube Data API v3
4. Create credentials (API Key)
5. Update `YOUTUBE_API_KEY` in `MediaExtractor.ts`

### SoundCloud API

1. Go to [SoundCloud Developers](https://developers.soundcloud.com/)
2. Register your application
3. Get your Client ID
4. Update `SOUNDCLOUD_CLIENT_ID` in `MediaExtractor.ts`

## Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── SongCard.tsx
│   ├── PlaylistCard.tsx
│   ├── RatingStars.tsx
│   ├── StatsCard.tsx
│   └── AnalysisResultCard.tsx
├── context/            # React Context providers
│   └── DatabaseContext.tsx
├── database/           # Database models and operations
│   └── Database.ts
├── screens/            # Main app screens
│   ├── HomeScreen.tsx
│   ├── AnalyzeScreen.tsx
│   ├── PlaylistsScreen.tsx
│   └── SettingsScreen.tsx
├── services/           # Core business logic
│   ├── AudioAnalyzer.ts
│   ├── MediaExtractor.ts
│   └── PlaylistManager.ts
└── theme/              # App theming
    └── theme.ts
```

## Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- FFT.js for Fast Fourier Transform implementation
- React Native Paper for Material Design components
- Expo team for the excellent development platform
- Music Information Retrieval community for audio analysis techniques

## Support

For support, email <EMAIL> or create an issue on GitHub.

## Roadmap

- [ ] Machine learning-based genre classification
- [ ] Real-time audio analysis
- [ ] Cloud sync for playlists
- [ ] Social features and playlist sharing
- [ ] Advanced audio effects visualization
- [ ] Integration with streaming services
- [ ] Desktop companion app
