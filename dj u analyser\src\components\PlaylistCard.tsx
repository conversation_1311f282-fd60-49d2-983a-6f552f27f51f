import React, { useState, useEffect } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import {
  Card,
  Text,
  IconButton,
  Menu,
  Divider,
} from 'react-native-paper';
import { Playlist } from '../database/Database';
import { useDatabase } from '../context/DatabaseContext';

interface PlaylistCardProps {
  playlist: Playlist;
  onPress?: () => void;
  onDelete?: () => void;
}

const PlaylistCard: React.FC<PlaylistCardProps> = ({
  playlist,
  onPress,
  onDelete,
}) => {
  const { getPlaylistSongs } = useDatabase();
  const [menuVisible, setMenuVisible] = useState(false);
  const [songCount, setSongCount] = useState(0);

  useEffect(() => {
    loadSongCount();
  }, [playlist.id]);

  const loadSongCount = async () => {
    try {
      const songs = await getPlaylistSongs(playlist.id);
      setSongCount(songs.length);
    } catch (error) {
      console.error('Failed to load song count:', error);
    }
  };

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  const handleViewPlaylist = () => {
    setMenuVisible(false);
    if (onPress) {
      onPress();
    } else {
      // Navigate to playlist detail screen
      Alert.alert('Info', 'Playlist detail view coming soon');
    }
  };

  const handleShare = () => {
    setMenuVisible(false);
    Alert.alert('Share', 'Playlist sharing feature coming soon');
  };

  const handleEdit = () => {
    setMenuVisible(false);
    Alert.alert('Edit', 'Playlist editing feature coming soon');
  };

  const handleDelete = () => {
    setMenuVisible(false);
    if (onDelete) {
      onDelete();
    }
  };

  return (
    <Card style={styles.card} onPress={handleViewPlaylist}>
      <Card.Content>
        <View style={styles.header}>
          <View style={styles.titleContainer}>
            <Text style={styles.title} numberOfLines={1}>
              {playlist.name}
            </Text>
            {playlist.description && (
              <Text style={styles.description} numberOfLines={2}>
                {playlist.description}
              </Text>
            )}
          </View>
          
          <Menu
            visible={menuVisible}
            onDismiss={() => setMenuVisible(false)}
            anchor={
              <IconButton
                icon="dots-vertical"
                iconColor="#ffffff"
                onPress={() => setMenuVisible(true)}
              />
            }
          >
            <Menu.Item
              onPress={handleViewPlaylist}
              title="View Playlist"
            />
            <Menu.Item
              onPress={handleEdit}
              title="Edit"
            />
            <Menu.Item
              onPress={handleShare}
              title="Share"
            />
            <Divider />
            <Menu.Item
              onPress={handleDelete}
              title="Delete"
            />
          </Menu>
        </View>

        <View style={styles.infoRow}>
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Songs</Text>
            <Text style={styles.infoValue}>{songCount}</Text>
          </View>
          
          <View style={styles.infoItem}>
            <Text style={styles.infoLabel}>Created</Text>
            <Text style={styles.infoValue}>
              {formatDate(playlist.createdAt)}
            </Text>
          </View>
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#16213e',
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 12,
  },
  titleContainer: {
    flex: 1,
    marginRight: 8,
  },
  title: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  description: {
    color: '#cccccc',
    fontSize: 14,
    lineHeight: 20,
  },
  infoRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  infoItem: {
    alignItems: 'center',
  },
  infoLabel: {
    color: '#cccccc',
    fontSize: 12,
    marginBottom: 2,
  },
  infoValue: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
  },
});

export default PlaylistCard;
