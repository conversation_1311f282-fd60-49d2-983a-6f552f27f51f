import React, { useState, useEffect } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  RefreshControl,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Searchbar,
  FAB,
  ActivityIndicator,
  Chip,
} from 'react-native-paper';
import { useDatabase } from '../context/DatabaseContext';
import { Song } from '../database/Database';
import SongCard from '../components/SongCard';
import StatsCard from '../components/StatsCard';

const HomeScreen: React.FC = () => {
  const {
    songs,
    loading,
    error,
    searchSongs,
    refreshSongs,
    getAnalysisStats,
  } = useDatabase();

  const [searchQuery, setSearchQuery] = useState('');
  const [filteredSongs, setFilteredSongs] = useState<Song[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [selectedGenre, setSelectedGenre] = useState<string | null>(null);
  const [stats, setStats] = useState<{
    totalSongs: number;
    avgBpm: number;
    genreDistribution: { [key: string]: number };
  } | null>(null);

  useEffect(() => {
    setFilteredSongs(songs);
    loadStats();
  }, [songs]);

  useEffect(() => {
    handleSearch();
  }, [searchQuery, selectedGenre, songs]);

  const loadStats = async () => {
    try {
      const analysisStats = await getAnalysisStats();
      setStats(analysisStats);
    } catch (err) {
      console.error('Failed to load stats:', err);
    }
  };

  const handleSearch = async () => {
    try {
      let results = songs;

      if (searchQuery.trim()) {
        results = await searchSongs(searchQuery);
      }

      if (selectedGenre) {
        results = results.filter(song => song.genre === selectedGenre);
      }

      setFilteredSongs(results);
    } catch (err) {
      Alert.alert('Error', 'Failed to search songs');
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await refreshSongs();
      await loadStats();
    } catch (err) {
      Alert.alert('Error', 'Failed to refresh data');
    } finally {
      setRefreshing(false);
    }
  };

  const getUniqueGenres = (): string[] => {
    const genres = songs
      .map(song => song.genre)
      .filter((genre): genre is string => Boolean(genre));
    return [...new Set(genres)];
  };

  if (loading && songs.length === 0) {
    return (
      <View style={styles.centerContainer}>
        <ActivityIndicator size="large" />
        <Text style={styles.loadingText}>Loading your music library...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Search Bar */}
        <Searchbar
          placeholder="Search songs, artists, or genres..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchBar}
        />

        {/* Stats Card */}
        {stats && <StatsCard stats={stats} />}

        {/* Genre Filter */}
        {getUniqueGenres().length > 0 && (
          <View style={styles.genreContainer}>
            <Text style={styles.sectionTitle}>Filter by Genre</Text>
            <ScrollView horizontal showsHorizontalScrollIndicator={false}>
              <Chip
                selected={selectedGenre === null}
                onPress={() => setSelectedGenre(null)}
                style={styles.genreChip}
                textStyle={styles.chipText}
              >
                All
              </Chip>
              {getUniqueGenres().map(genre => (
                <Chip
                  key={genre}
                  selected={selectedGenre === genre}
                  onPress={() => setSelectedGenre(genre)}
                  style={styles.genreChip}
                  textStyle={styles.chipText}
                >
                  {genre}
                </Chip>
              ))}
            </ScrollView>
          </View>
        )}

        {/* Songs List */}
        <View style={styles.songsContainer}>
          <Text style={styles.sectionTitle}>
            Your Music ({filteredSongs.length} songs)
          </Text>
          
          {filteredSongs.length === 0 ? (
            <Card style={styles.emptyCard}>
              <Card.Content>
                <Text style={styles.emptyText}>
                  {searchQuery || selectedGenre
                    ? 'No songs match your search criteria'
                    : 'No songs in your library yet. Tap the + button to add some music!'}
                </Text>
              </Card.Content>
            </Card>
          ) : (
            filteredSongs.map(song => (
              <SongCard key={song.id} song={song} />
            ))
          )}
        </View>
      </ScrollView>

      {/* Add Song FAB */}
      <FAB
        style={styles.fab}
        icon="plus"
        onPress={() => {
          // Navigate to analyze screen
          // This would be handled by navigation in a real app
          Alert.alert('Add Song', 'Navigate to Analyze tab to add new songs');
        }}
      />

      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>{error}</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#1a1a2e',
  },
  scrollView: {
    flex: 1,
  },
  searchBar: {
    margin: 16,
    backgroundColor: '#16213e',
  },
  genreContainer: {
    marginHorizontal: 16,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#ffffff',
    marginBottom: 12,
  },
  genreChip: {
    marginRight: 8,
    backgroundColor: '#16213e',
  },
  chipText: {
    color: '#ffffff',
  },
  songsContainer: {
    marginHorizontal: 16,
    marginBottom: 80, // Space for FAB
  },
  emptyCard: {
    backgroundColor: '#16213e',
    marginVertical: 8,
  },
  emptyText: {
    color: '#ffffff',
    textAlign: 'center',
    fontSize: 16,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
    backgroundColor: '#0f3460',
  },
  loadingText: {
    color: '#ffffff',
    marginTop: 16,
    fontSize: 16,
  },
  errorContainer: {
    position: 'absolute',
    bottom: 100,
    left: 16,
    right: 16,
    backgroundColor: '#d32f2f',
    padding: 12,
    borderRadius: 8,
  },
  errorText: {
    color: '#ffffff',
    textAlign: 'center',
  },
});

export default HomeScreen;
