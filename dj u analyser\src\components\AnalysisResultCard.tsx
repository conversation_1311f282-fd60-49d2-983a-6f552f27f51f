import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { Card, Text, Chip } from 'react-native-paper';
import { LineChart } from 'react-native-chart-kit';
import { AnalysisResult } from '../services/AudioAnalyzer';
import { MediaInfo } from '../services/MediaExtractor';

interface AnalysisResultCardProps {
  result: AnalysisResult;
  mediaInfo: MediaInfo;
}

const AnalysisResultCard: React.FC<AnalysisResultCardProps> = ({
  result,
  mediaInfo,
}) => {
  const screenWidth = Dimensions.get('window').width;
  const chartWidth = screenWidth - 64;

  const chartConfig = {
    backgroundGradientFrom: '#16213e',
    backgroundGradientTo: '#16213e',
    color: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
    labelColor: (opacity = 1) => `rgba(255, 255, 255, ${opacity})`,
    strokeWidth: 2,
    barPercentage: 0.5,
    useShadowColorFromDataset: false,
    decimalPlaces: 2,
  };

  const mfccData = {
    labels: result.features.mfcc.slice(0, 6).map((_, i) => `${i + 1}`),
    datasets: [
      {
        data: result.features.mfcc.slice(0, 6),
        color: (opacity = 1) => `rgba(81, 149, 255, ${opacity})`,
        strokeWidth: 2,
      },
    ],
  };

  const chromaData = {
    labels: ['C', 'C#', 'D', 'D#', 'E', 'F'],
    datasets: [
      {
        data: result.features.chroma.slice(0, 6),
        color: (opacity = 1) => `rgba(255, 99, 132, ${opacity})`,
        strokeWidth: 2,
      },
    ],
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return '#4caf50';
    if (confidence >= 0.6) return '#ff9800';
    return '#f44336';
  };

  const formatConfidence = (confidence: number) => {
    return `${Math.round(confidence * 100)}%`;
  };

  return (
    <Card style={styles.card}>
      <Card.Title title="Analysis Results" titleStyle={styles.cardTitle} />
      <Card.Content>
        {/* Media Info */}
        <View style={styles.mediaInfo}>
          <Text style={styles.title}>{mediaInfo.title}</Text>
          <Text style={styles.artist}>{mediaInfo.artist}</Text>
        </View>

        {/* Main Results */}
        <View style={styles.resultsGrid}>
          <View style={styles.resultItem}>
            <Text style={styles.resultLabel}>BPM</Text>
            <Text style={styles.resultValue}>{result.bpm}</Text>
            <Text
              style={[
                styles.confidence,
                { color: getConfidenceColor(result.confidence.bpm) },
              ]}
            >
              {formatConfidence(result.confidence.bpm)}
            </Text>
          </View>

          <View style={styles.resultItem}>
            <Text style={styles.resultLabel}>Key</Text>
            <Text style={styles.resultValue}>{result.key}</Text>
            <Text
              style={[
                styles.confidence,
                { color: getConfidenceColor(result.confidence.key) },
              ]}
            >
              {formatConfidence(result.confidence.key)}
            </Text>
          </View>

          <View style={styles.resultItem}>
            <Text style={styles.resultLabel}>Genre</Text>
            <Text style={styles.resultValue}>{result.genre}</Text>
            <Text
              style={[
                styles.confidence,
                { color: getConfidenceColor(result.confidence.genre) },
              ]}
            >
              {formatConfidence(result.confidence.genre)}
            </Text>
          </View>
        </View>

        {/* Audio Features */}
        <View style={styles.featuresContainer}>
          <Text style={styles.sectionTitle}>Audio Features</Text>
          
          <View style={styles.featureRow}>
            <Chip style={styles.featureChip} textStyle={styles.chipText}>
              Spectral Centroid: {Math.round(result.features.spectralCentroid)} Hz
            </Chip>
            <Chip style={styles.featureChip} textStyle={styles.chipText}>
              Spectral Rolloff: {Math.round(result.features.spectralRolloff)} Hz
            </Chip>
          </View>
          
          <View style={styles.featureRow}>
            <Chip style={styles.featureChip} textStyle={styles.chipText}>
              Tempo: {Math.round(result.features.tempo)} BPM
            </Chip>
          </View>
        </View>

        {/* MFCC Chart */}
        <View style={styles.chartContainer}>
          <Text style={styles.chartTitle}>MFCC Coefficients (First 6)</Text>
          <LineChart
            data={mfccData}
            width={chartWidth}
            height={180}
            chartConfig={chartConfig}
            bezier
            style={styles.chart}
          />
        </View>

        {/* Chroma Chart */}
        <View style={styles.chartContainer}>
          <Text style={styles.chartTitle}>Chroma Features (First 6)</Text>
          <LineChart
            data={chromaData}
            width={chartWidth}
            height={180}
            chartConfig={chartConfig}
            bezier
            style={styles.chart}
          />
        </View>
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#16213e',
    marginBottom: 16,
  },
  cardTitle: {
    color: '#ffffff',
  },
  mediaInfo: {
    marginBottom: 20,
    alignItems: 'center',
  },
  title: {
    color: '#ffffff',
    fontSize: 18,
    fontWeight: 'bold',
    textAlign: 'center',
  },
  artist: {
    color: '#cccccc',
    fontSize: 14,
    textAlign: 'center',
    marginTop: 4,
  },
  resultsGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginBottom: 20,
  },
  resultItem: {
    alignItems: 'center',
    flex: 1,
  },
  resultLabel: {
    color: '#cccccc',
    fontSize: 12,
    marginBottom: 4,
  },
  resultValue: {
    color: '#ffffff',
    fontSize: 20,
    fontWeight: 'bold',
    marginBottom: 4,
  },
  confidence: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  featuresContainer: {
    marginBottom: 20,
  },
  sectionTitle: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  featureRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  featureChip: {
    backgroundColor: '#0f3460',
    marginRight: 8,
    marginBottom: 4,
  },
  chipText: {
    color: '#ffffff',
    fontSize: 10,
  },
  chartContainer: {
    marginBottom: 20,
    alignItems: 'center',
  },
  chartTitle: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: 'bold',
    marginBottom: 10,
  },
  chart: {
    borderRadius: 8,
  },
});

export default AnalysisResultCard;
