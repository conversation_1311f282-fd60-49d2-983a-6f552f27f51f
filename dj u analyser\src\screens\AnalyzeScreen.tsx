import React, { useState } from 'react';
import {
  View,
  ScrollView,
  StyleSheet,
  Alert,
} from 'react-native';
import {
  Text,
  Card,
  Button,
  TextInput,
  ActivityIndicator,
  ProgressBar,
} from 'react-native-paper';
import * as DocumentPicker from 'expo-document-picker';
import { useDatabase } from '../context/DatabaseContext';
import { AudioAnalyzer, AnalysisResult } from '../services/AudioAnalyzer';
import { MediaExtractor, AudioData } from '../services/MediaExtractor';
import AnalysisResultCard from '../components/AnalysisResultCard';

const AnalyzeScreen: React.FC = () => {
  const { addSong } = useDatabase();
  const [url, setUrl] = useState('');
  const [analyzing, setAnalyzing] = useState(false);
  const [progress, setProgress] = useState(0);
  const [currentStep, setCurrentStep] = useState('');
  const [analysisResult, setAnalysisResult] = useState<AnalysisResult | null>(null);
  const [audioData, setAudioData] = useState<AudioData | null>(null);

  const audioAnalyzer = new AudioAnalyzer();
  const mediaExtractor = new MediaExtractor();

  const analyzeFromUrl = async () => {
    if (!url.trim()) {
      Alert.alert('Error', 'Please enter a valid URL');
      return;
    }

    setAnalyzing(true);
    setProgress(0);
    setAnalysisResult(null);
    setAudioData(null);

    try {
      // Validate URL
      setCurrentStep('Validating URL...');
      setProgress(0.1);
      
      const isValid = await mediaExtractor.validateUrl(url);
      if (!isValid) {
        throw new Error('Invalid or unsupported URL');
      }

      // Extract audio
      setCurrentStep('Extracting audio...');
      setProgress(0.3);
      
      const extractedAudio = await mediaExtractor.extractFromUrl(url);
      setAudioData(extractedAudio);

      // Analyze audio
      setCurrentStep('Analyzing audio features...');
      setProgress(0.6);
      
      const result = await audioAnalyzer.analyzeAudio(extractedAudio.audioBuffer);
      setAnalysisResult(result);

      setCurrentStep('Analysis complete!');
      setProgress(1.0);

      Alert.alert('Success', 'Audio analysis completed successfully!');
    } catch (error) {
      console.error('Analysis error:', error);
      Alert.alert('Error', error instanceof Error ? error.message : 'Failed to analyze audio');
    } finally {
      setAnalyzing(false);
      setCurrentStep('');
    }
  };

  const analyzeFromFile = async () => {
    try {
      const result = await DocumentPicker.getDocumentAsync({
        type: 'audio/*',
        copyToCacheDirectory: true,
      });

      if (result.type === 'cancel') {
        return;
      }

      setAnalyzing(true);
      setProgress(0);
      setAnalysisResult(null);
      setAudioData(null);

      // Extract audio from file
      setCurrentStep('Loading audio file...');
      setProgress(0.2);
      
      const extractedAudio = await mediaExtractor.extractFromFile(result.uri);
      setAudioData(extractedAudio);

      // Analyze audio
      setCurrentStep('Analyzing audio features...');
      setProgress(0.6);
      
      const analysisResult = await audioAnalyzer.analyzeAudio(extractedAudio.audioBuffer);
      setAnalysisResult(analysisResult);

      setCurrentStep('Analysis complete!');
      setProgress(1.0);

      Alert.alert('Success', 'Audio analysis completed successfully!');
    } catch (error) {
      console.error('File analysis error:', error);
      Alert.alert('Error', error instanceof Error ? error.message : 'Failed to analyze audio file');
    } finally {
      setAnalyzing(false);
      setCurrentStep('');
    }
  };

  const saveToLibrary = async () => {
    if (!audioData || !analysisResult) {
      Alert.alert('Error', 'No analysis data to save');
      return;
    }

    try {
      await addSong(audioData.mediaInfo, analysisResult);
      Alert.alert('Success', 'Song added to your library!');
      
      // Reset state
      setUrl('');
      setAnalysisResult(null);
      setAudioData(null);
    } catch (error) {
      console.error('Save error:', error);
      Alert.alert('Error', 'Failed to save song to library');
    }
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.content}>
        {/* URL Input Section */}
        <Card style={styles.card}>
          <Card.Title title="Analyze from URL" titleStyle={styles.cardTitle} />
          <Card.Content>
            <TextInput
              label="YouTube or SoundCloud URL"
              value={url}
              onChangeText={setUrl}
              mode="outlined"
              style={styles.input}
              placeholder="https://www.youtube.com/watch?v=..."
              disabled={analyzing}
            />
            <Button
              mode="contained"
              onPress={analyzeFromUrl}
              disabled={analyzing || !url.trim()}
              style={styles.button}
            >
              Analyze from URL
            </Button>
          </Card.Content>
        </Card>

        {/* File Input Section */}
        <Card style={styles.card}>
          <Card.Title title="Analyze from File" titleStyle={styles.cardTitle} />
          <Card.Content>
            <Text style={styles.description}>
              Select an audio file from your device to analyze
            </Text>
            <Button
              mode="contained"
              onPress={analyzeFromFile}
              disabled={analyzing}
              style={styles.button}
              icon="file-music"
            >
              Choose Audio File
            </Button>
          </Card.Content>
        </Card>

        {/* Progress Section */}
        {analyzing && (
          <Card style={styles.card}>
            <Card.Content>
              <Text style={styles.progressText}>{currentStep}</Text>
              <ProgressBar progress={progress} style={styles.progressBar} />
              <ActivityIndicator style={styles.spinner} />
            </Card.Content>
          </Card>
        )}

        {/* Analysis Results */}
        {analysisResult && audioData && (
          <View>
            <AnalysisResultCard
              result={analysisResult}
              mediaInfo={audioData.mediaInfo}
            />
            
            <Card style={styles.card}>
              <Card.Content>
                <Button
                  mode="contained"
                  onPress={saveToLibrary}
                  style={styles.saveButton}
                  icon="content-save"
                >
                  Save to Library
                </Button>
              </Card.Content>
            </Card>
          </View>
        )}

        {/* Instructions */}
        <Card style={styles.card}>
          <Card.Title title="How to Use" titleStyle={styles.cardTitle} />
          <Card.Content>
            <Text style={styles.instructionText}>
              • Paste a YouTube or SoundCloud URL to analyze online content
            </Text>
            <Text style={styles.instructionText}>
              • Or select an audio file from your device
            </Text>
            <Text style={styles.instructionText}>
              • The AI will analyze BPM, key, genre, and other features
            </Text>
            <Text style={styles.instructionText}>
              • Save analyzed songs to your library for easy access
            </Text>
          </Card.Content>
        </Card>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#1a1a2e',
  },
  content: {
    padding: 16,
  },
  card: {
    backgroundColor: '#16213e',
    marginBottom: 16,
  },
  cardTitle: {
    color: '#ffffff',
  },
  input: {
    marginBottom: 16,
    backgroundColor: '#1a1a2e',
  },
  button: {
    backgroundColor: '#0f3460',
  },
  saveButton: {
    backgroundColor: '#2e7d32',
  },
  description: {
    color: '#ffffff',
    marginBottom: 16,
    fontSize: 14,
  },
  progressText: {
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 8,
    fontSize: 16,
  },
  progressBar: {
    marginBottom: 16,
  },
  spinner: {
    marginTop: 8,
  },
  instructionText: {
    color: '#ffffff',
    fontSize: 14,
    marginBottom: 8,
  },
});

export default AnalyzeScreen;
