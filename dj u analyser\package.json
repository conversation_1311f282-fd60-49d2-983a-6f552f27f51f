{"name": "dj-analyzer", "version": "1.0.0", "description": "Autonomous AI agent for music track analysis with BPM, key, and genre detection", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "build:android": "eas build --platform android", "build:ios": "eas build --platform ios", "test": "jest", "lint": "eslint ."}, "dependencies": {"@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "@tensorflow/tfjs": "^4.10.0", "@tensorflow/tfjs-react-native": "^0.8.0", "axios": "^1.5.0", "expo": "~49.0.0", "expo-av": "~13.4.1", "expo-constants": "~14.4.2", "expo-crypto": "~12.4.1", "expo-document-picker": "~11.5.4", "expo-file-system": "~15.4.4", "expo-media-library": "~15.4.1", "expo-sharing": "~11.5.0", "expo-sqlite": "~11.3.3", "expo-status-bar": "~1.6.0", "fft-js": "^0.0.12", "ml-matrix": "^6.10.4", "react": "18.2.0", "react-native": "0.72.6", "react-native-chart-kit": "^6.12.0", "react-native-fs": "^2.20.0", "react-native-gesture-handler": "~2.12.0", "react-native-paper": "^5.10.6", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-svg": "13.9.0", "react-native-uuid": "^2.0.1", "react-native-vector-icons": "^10.0.0", "react-navigation": "^4.4.4", "soundcloud-downloader": "^1.0.0", "ytdl-core": "^4.11.5"}, "devDependencies": {"@babel/core": "^7.20.0", "@eslint/js": "^9.36.0", "@types/jest": "^30.0.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "@typescript-eslint/eslint-plugin": "^8.44.0", "@typescript-eslint/parser": "^8.44.0", "eslint": "^8.45.0", "eslint-config-expo": "^7.0.0", "eslint-plugin-n8n-nodes-base": "^1.16.3", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^5.0.0", "jest": "^29.6.1", "jest-expo": "^54.0.12", "ts-jest": "^29.4.4", "typescript": "^5.9.2"}, "keywords": ["music", "audio-analysis", "bpm", "key-detection", "genre-classification", "react-native", "mobile-app"], "author": "DJ Analyzer Team", "license": "MIT", "private": true, "type": "module"}